{
  "cli": {
    "version": ">= 16.0.0",
    "appVersionSource": "remote"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleDebug"
      },
      "env": {
        "NODE_ENV": "development",
        "EXPO_USE_METRO_WORKSPACE_ROOT": "1",
        "EAS_SKIP_AUTO_FINGERPRINT": "1"
      }
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      },
      "env": {
        "EAS_SKIP_AUTO_FINGERPRINT": "1",

        "EXPO_PUBLIC_FIREBASE_API_KEY": "AIzaSyB6ALvnN6aX0DMVhePhkUow9VrPauBCqgQ",
        "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "tap2go-kuucn.firebaseapp.com",
        "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "tap2go-kuucn",
        "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "tap2go-kuucn.firebasestorage.app",
        "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "828629511294",
        "EXPO_PUBLIC_FIREBASE_APP_ID": "1:828629511294:web:fae32760ca3c3afcb87c2f",
        "EXPO_PUBLIC_FIREBASE_VAPID_KEY": "BIZ720hEPOJI1onp93mfqutx5ceyFakOJPRM8R-Oa8eJibI5jsntq4PH-erjRy502Ac823zPQ63BTV5_qWxQUoQ",


*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        "EXPO_PUBLIC_FIREBASE_ADMIN_CLIENT_EMAIL": "<EMAIL>",

        "EXPO_PUBLIC_MAPS_FRONTEND_KEY": "AIzaSyDWWpv5PBQFpfIkHmtOnHTGktHv5o36Cnw",
        "EXPO_PUBLIC_MAPS_BACKEND_KEY": "AIzaSyAhGLoNGg-gMgpDmiMdVk2POptR219SGT4",

        "EXPO_PUBLIC_BONSAI_HOST": "https://tap2go-search-**********.ap-southeast-2.bonsaisearch.net:443",
        "EXPO_PUBLIC_BONSAI_USERNAME": "SF5gQKJtVA",
        "EXPO_PUBLIC_BONSAI_PASSWORD": "bKJCacZSz8h27HxjXEeYVd",

        "EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME": "dpekh75yi",
        "EXPO_PUBLIC_CLOUDINARY_API_KEY": "191284661715922",
        "EXPO_PUBLIC_CLOUDINARY_API_SECRET": "G-_izp68I2eJuZOCvAKOmPkTXdI",
        "EXPO_PUBLIC_CLOUDINARY_WEBHOOK_SECRET": "your_webhook_secret_here",

        "EXPO_PUBLIC_PAYMONGO_PUBLIC_KEY_LIVE": "********************************",
        "EXPO_PUBLIC_PAYMONGO_SECRET_KEY_LIVE": "********************************",

        "EXPO_PUBLIC_SUPABASE_URL": "https://iblujnytqusttngujhob.supabase.co",
        "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlibHVqbnl0cXVzdHRuZ3VqaG9iIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyNjQxNjMsImV4cCI6MjA2NDg0MDE2M30.zt4RCWmS3POQtULRl27UcdGGCPGbYPJvTr8l6YlOhvA",
        "EXPO_PUBLIC_SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlibHVqbnl0cXVzdHRuZ3VqaG9iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTI2NDE2MywiZXhwIjoyMDY0ODQwMTYzfQ.xTRzQ5JAKUkdEDTZyW9A5qD3IfuNqSLYlnJc8xR2_Do",

        "EXPO_PUBLIC_UPSTASH_REDIS_REST_URL": "https://brief-glider-38999.upstash.io",
        "EXPO_PUBLIC_UPSTASH_REDIS_REST_TOKEN": "AZhXAAIjcDEyNDI2YWYxOGVhMGI0MjRjYWIzY2JmMGQxZDhjYTExM3AxMA",

        "EXPO_PUBLIC_RESEND_FROM_EMAIL": "<EMAIL>",
        "EXPO_PUBLIC_RESEND_API_KEY": "re_SZwhCkVr_Nq5yjCJQP812Kg8qJP7AZKBU",

        "EXPO_PUBLIC_GOOGLE_AI_API_KEY": "AIzaSyBGv6UYOc9N8pZ1XfY88H9Tit301vjLMtw",

        "EXPO_PUBLIC_ENABLE_SUPABASE_CMS": "true",
        "EXPO_PUBLIC_ENABLE_REDIS_CACHING": "true",
        "EXPO_PUBLIC_ENABLE_EMAIL_NOTIFICATIONS": "true",
        "EXPO_PUBLIC_ENABLE_AI_FEATURES": "true",
        "EXPO_PUBLIC_AI_MODEL_DEFAULT": "gemini-1.5-flash",
        "EXPO_PUBLIC_EMAIL_FROM_NAME": "Tap2Go",
        "EXPO_PUBLIC_EMAIL_REPLY_TO": "<EMAIL>",
        "EXPO_PUBLIC_REDIS_DEFAULT_TTL": "3600",
        "EXPO_BETA": "false",
        "EXPO_USE_FAST_RESOLVER": "true",
        "EXPO_CACHE_CERTIFICATES": "true",

        "MAPS_BACKEND_KEY": "AIzaSyAhGLoNGg-gMgpDmiMdVk2POptR219SGT4",
        "BONSAI_HOST": "https://tap2go-search-**********.ap-southeast-2.bonsaisearch.net:443",
        "BONSAI_USERNAME": "SF5gQKJtVA",
        "BONSAI_PASSWORD": "bKJCacZSz8h27HxjXEeYVd",
        "CLOUDINARY_API_KEY": "191284661715922",
        "CLOUDINARY_API_SECRET": "G-_izp68I2eJuZOCvAKOmPkTXdI",
        "CLOUDINARY_WEBHOOK_SECRET": "your_webhook_secret_here",
        "PAYMONGO_SECRET_KEY_LIVE": "********************************",
        "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlibHVqbnl0cXVzdHRuZ3VqaG9iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTI2NDE2MywiZXhwIjoyMDY0ODQwMTYzfQ.xTRzQ5JAKUkdEDTZyW9A5qD3IfuNqSLYlnJc8xR2_Do",
        "UPSTASH_REDIS_REST_URL": "https://brief-glider-38999.upstash.io",
        "UPSTASH_REDIS_REST_TOKEN": "AZhXAAIjcDEyNDI2YWYxOGVhMGI0MjRjYWIzY2JmMGQxZDhjYTExM3AxMA",
        "RESEND_API_KEY": "re_SZwhCkVr_Nq5yjCJQP812Kg8qJP7AZKBU",
        "GOOGLE_AI_API_KEY": "AIzaSyBGv6UYOc9N8pZ1XfY88H9Tit301vjLMtw"
      },

    },
    "production": {
      "android": {
        "buildType": "app-bundle",
        "gradleCommand": ":app:bundleRelease"
      },
      "env": {
        "NODE_ENV": "production",
        "EAS_BUILD": "true",
        "EXPO_USE_METRO_WORKSPACE_ROOT": "1",
        "METRO_CONFIG": "./metro.config.eas.js"
      }
    }
  },
  "submit": {
    "production": {
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "internal"
      }
    }
  }
}
