const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');
const fs = require('fs');

// PERMANENT SOLUTION: Bulletproof Metro config for monorepo
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// Essential monorepo configuration
config.watchFolders = [projectRoot, monorepoRoot];
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// PERMANENT FIX: Custom resolver to handle App resolution
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // CRITICAL: Handle the "../../App" resolution issue that causes bundling failures
  if (moduleName === '../../App' || moduleName.endsWith('/App') || moduleName === '../App' || moduleName === './App') {
    const appPath = path.resolve(projectRoot, 'App.tsx');
    if (fs.existsSync(appPath)) {
      return {
        filePath: appPath,
        type: 'sourceFile',
      };
    }
    // Fallback to App.js
    const appJsPath = path.resolve(projectRoot, 'App.js');
    if (fs.existsSync(appJsPath)) {
      return {
        filePath: appJsPath,
        type: 'sourceFile',
      };
    }
  }

  // Handle any relative imports that might fail
  if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
    const originDir = context.originModulePath ? path.dirname(context.originModulePath) : projectRoot;
    const resolvedPath = path.resolve(originDir, moduleName);
    
    // Try different extensions
    const extensions = ['.tsx', '.ts', '.jsx', '.js', '.json'];
    for (const ext of extensions) {
      const fullPath = resolvedPath + ext;
      if (fs.existsSync(fullPath)) {
        return {
          filePath: fullPath,
          type: 'sourceFile',
        };
      }
    }
    
    // Try as directory with index file
    for (const ext of extensions) {
      const indexPath = path.resolve(resolvedPath, 'index' + ext);
      if (fs.existsSync(indexPath)) {
        return {
          filePath: indexPath,
          type: 'sourceFile',
        };
      }
    }
  }

  // Default resolution
  return context.resolveRequest(context, moduleName, platform);
};

// Apply NativeWind
try {
  const { withNativeWind } = require('nativewind/metro');
  module.exports = withNativeWind(config, {
    input: './global.css',
    configPath: './tailwind.config.js',
  });
} catch (error) {
  module.exports = config;
}
