const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default Expo Metro config
const config = getDefaultConfig(__dirname);

// Configure for monorepo structure
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

// Set up watch folders for monorepo
config.watchFolders = [monorepoRoot];

// Configure node modules resolution
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// Configure platforms
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Apply NativeWind configuration if available
try {
  const { withNativeWind } = require('nativewind/metro');
  module.exports = withNativeWind(config, {
    input: './global.css',
    configPath: './tailwind.config.js',
  });
} catch (error) {
  console.warn('NativeWind not available, using default config');
  module.exports = config;
}
