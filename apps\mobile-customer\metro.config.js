const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default Expo Metro config
const config = getDefaultConfig(__dirname);

// Configure for monorepo structure
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

// Set up watch folders for monorepo
config.watchFolders = [monorepoRoot];

// Configure node modules resolution
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// Configure platforms
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Apply NativeWind configuration
try {
  // Try different paths for NativeWind in monorepo
  let withNativeWind;
  try {
    withNativeWind = require('nativewind/metro').withNativeWind;
  } catch (e1) {
    try {
      withNativeWind = require(path.resolve(projectRoot, 'node_modules/nativewind/metro')).withNativeWind;
    } catch (e2) {
      withNativeWind = require(path.resolve(monorepoRoot, 'node_modules/nativewind/metro')).withNativeWind;
    }
  }

  console.log('✅ NativeWind found and configured');
  module.exports = withNativeWind(config, {
    input: './global.css',
    configPath: './tailwind.config.js',
  });
} catch (error) {
  console.warn('⚠️ NativeWind not available:', error.message);
  console.warn('Using default config without NativeWind');
  module.exports = config;
}
